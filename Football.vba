Sub CreateFootballAnalyticsPPT()
    Dim pptApp As PowerPoint.Application
    Dim pptPres As PowerPoint.Presentation
    Dim pptSlide As PowerPoint.Slide
    Dim pptLayout As PowerPoint.CustomLayout
    Dim shp As PowerPoint.Shape
    Dim i As Integer
    
    ' Create new PowerPoint application and presentation
    Set pptApp = New PowerPoint.Application
    pptApp.Visible = True
    Set pptPres = pptApp.Presentations.Add
    
    ' Set presentation properties
    With pptPres
        .PageSetup.SlideSize = ppSlideSizeOnScreen16x9
        .SlideMaster.ColorScheme.Colors(ppBackground).RGB = RGB(15, 23, 42) ' Dark slate
        .SlideMaster.ColorScheme.Colors(ppForeground).RGB = RGB(248, 250, 252) ' Light text
    End With
    
    ' Slide 1: Title Slide
    Set pptSlide = pptPres.Slides.Add(1, ppLayoutBlank)
    Call CreateTitleSlide(pptSlide)
    
    ' Slide 2: Project Overview
    Set pptSlide = pptPres.Slides.Add(2, ppLayoutBlank)
    Call CreateOverviewSlide(pptSlide)
    
    ' Slide 3: Tech Stack Architecture
    Set pptSlide = pptPres.Slides.Add(3, ppLayoutBlank)
    Call CreateTechStackSlide(pptSlide)
    
    ' Slide 4: ML/CV Pipeline
    Set pptSlide = pptPres.Slides.Add(4, ppLayoutBlank)
    Call CreateMLPipelineSlide(pptSlide)
    
    ' Slide 5: Weekly Timeline
    Set pptSlide = pptPres.Slides.Add(5, ppLayoutBlank)
    Call CreateTimelineSlide(pptSlide)
    
    ' Slide 6: Analytics Features Matrix
    Set pptSlide = pptPres.Slides.Add(6, ppLayoutBlank)
    Call CreateFeaturesMatrixSlide(pptSlide)
    
    ' Slide 7: Player Analytics Dashboard
    Set pptSlide = pptPres.Slides.Add(7, ppLayoutBlank)
    Call CreatePlayerAnalyticsSlide(pptSlide)
    
    ' Slide 8: Team Tactical Analytics
    Set pptSlide = pptPres.Slides.Add(8, ppLayoutBlank)
    Call CreateTeamAnalyticsSlide(pptSlide)
    
    ' Slide 9: Real-time Processing Flow
    Set pptSlide = pptPres.Slides.Add(9, ppLayoutBlank)
    Call CreateProcessingFlowSlide(pptSlide)
    
    ' Slide 10: Deployment Architecture
    Set pptSlide = pptPres.Slides.Add(10, ppLayoutBlank)
    Call CreateDeploymentSlide(pptSlide)
    
    ' Slide 11: Future Roadmap
    Set pptSlide = pptPres.Slides.Add(11, ppLayoutBlank)
    Call CreateRoadmapSlide(pptSlide)
    
    ' Apply transitions to all slides
    Call ApplyTransitions(pptPres)
    
    MsgBox "AI-Powered Football Analytics System presentation created successfully!", vbInformation
End Sub

Sub CreateTitleSlide(slide As PowerPoint.Slide)
    Dim shp As PowerPoint.Shape
    
    ' Set slide background
    slide.Background.Fill.ForeColor.RGB = RGB(15, 23, 42)
    
    ' Main title with gradient effect
    Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 50, 150, 850, 120)
    With shp
        .TextFrame.TextRange.Text = "AI-POWERED FOOTBALL ANALYTICS SYSTEM"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 44
        .TextFrame.TextRange.Font.Bold = True
        .TextFrame.TextRange.ParagraphFormat.Alignment = ppAlignCenter
        .Fill.ForeColor.RGB = RGB(59, 130, 246) ' Blue
        .Line.Visible = msoFalse
        .Shadow.Type = msoShadowOffset
        .Shadow.OffsetX = 3
        .Shadow.OffsetY = 3
        .Shadow.ForeColor.RGB = RGB(0, 0, 0)
    End With
    
    ' Subtitle
    Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 100, 280, 750, 60)
    With shp
        .TextFrame.TextRange.Text = "Computer Vision • Machine Learning • Real-time Analytics"
        .TextFrame.TextRange.Font.Name = "Segoe UI Light"
        .TextFrame.TextRange.Font.Size = 24
        .TextFrame.TextRange.Font.Color.RGB = RGB(148, 163, 184)
        .TextFrame.TextRange.ParagraphFormat.Alignment = ppAlignCenter
        .Fill.Visible = msoFalse
        .Line.Visible = msoFalse
    End With
    
    ' Project specs
    Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 200, 380, 550, 100)
    With shp
        .TextFrame.TextRange.Text = "📍 10 Weeks | 🧑‍💻 Solo Development | 💻 2-3 Days/Week" & vbCrLf & "🎯 FIFA-Style Match Analytics from Smartphone Video"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 18
        .TextFrame.TextRange.Font.Color.RGB = RGB(248, 250, 252)
        .TextFrame.TextRange.ParagraphFormat.Alignment = ppAlignCenter
        .Fill.Visible = msoFalse
        .Line.Visible = msoFalse
    End With
    
    ' Add tech icons/shapes
    Call AddTechIcons(slide)
End Sub

Sub CreateOverviewSlide(slide As PowerPoint.Slide)
    Dim shp As PowerPoint.Shape
    
    slide.Background.Fill.ForeColor.RGB = RGB(15, 23, 42)
    
    ' Title
    Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 50, 30, 850, 60)
    With shp
        .TextFrame.TextRange.Text = "🎯 PROJECT OVERVIEW"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 36
        .TextFrame.TextRange.Font.Bold = True
        .TextFrame.TextRange.Font.Color.RGB = RGB(59, 130, 246)
        .Fill.Visible = msoFalse
        .Line.Visible = msoFalse
    End With
    
    ' Goal box
    Set shp = slide.Shapes.AddRoundedRectangle(50, 120, 400, 150)
    With shp
        .Fill.ForeColor.RGB = RGB(30, 41, 59)
        .Line.ForeColor.RGB = RGB(59, 130, 246)
        .Line.Weight = 2
        .TextFrame.TextRange.Text = "🚀 GOAL" & vbCrLf & vbCrLf & "Transform smartphone footage into professional-grade match analytics using cutting-edge ML/CV"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 16
        .TextFrame.TextRange.Font.Color.RGB = RGB(248, 250, 252)
        .TextFrame.MarginLeft = 20
        .TextFrame.MarginTop = 15
    End With
    
    ' Match Analytics
    Set shp = slide.Shapes.AddRoundedRectangle(650, 120, 280, 350)
    With shp
        .Fill.ForeColor.RGB = RGB(30, 41, 59)
        .Line.ForeColor.RGB = RGB(245, 158, 11)
        .Line.Weight = 2
        .TextFrame.TextRange.Text = "✅ MATCH ANALYTICS" & vbCrLf & vbCrLf & _
            "• Ball Possession Map" & vbCrLf & _
            "• Trajectory Smoothing" & vbCrLf & _
            "• Ball Speed Estimation" & vbCrLf & _
            "• Shot Detection" & vbCrLf & _
            "• Out-of-Play Detection" & vbCrLf & _
            "• Key Passes" & vbCrLf & _
            "• Timeline of Events" & vbCrLf & _
            "• Auto Highlights" & vbCrLf & _
            "• Fouls Estimation" & vbCrLf & _
            "• Ball-in-Play Time" & vbCrLf & _
            "• Match Summary Stats"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 12
        .TextFrame.TextRange.Font.Color.RGB = RGB(248, 250, 252)
        .TextFrame.MarginLeft = 15
        .TextFrame.MarginTop = 15
    End With
End Sub

Sub CreatePlayerAnalyticsSlide(slide As PowerPoint.Slide)
    Dim shp As PowerPoint.Shape
    
    slide.Background.Fill.ForeColor.RGB = RGB(15, 23, 42)
    
    ' Title
    Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 50, 30, 850, 60)
    With shp
        .TextFrame.TextRange.Text = "🏃‍♂️ PLAYER ANALYTICS DASHBOARD"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 36
        .TextFrame.TextRange.Font.Bold = True
        .TextFrame.TextRange.Font.Color.RGB = RGB(59, 130, 246)
        .Fill.Visible = msoFalse
        .Line.Visible = msoFalse
    End With
    
    ' Mock heatmap visualization
    Set shp = slide.Shapes.AddRoundedRectangle(50, 120, 400, 250)
    With shp
        .Fill.ForeColor.RGB = RGB(30, 41, 59)
        .Line.ForeColor.RGB = RGB(16, 185, 129)
        .Line.Weight = 2
    End With
    
    ' Heatmap title
    Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 70, 135, 360, 30)
    With shp
        .TextFrame.TextRange.Text = "🔥 PLAYER #10 HEATMAP"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 16
        .TextFrame.TextRange.Font.Bold = True
        .TextFrame.TextRange.Font.Color.RGB = RGB(16, 185, 129)
        .Fill.Visible = msoFalse
        .Line.Visible = msoFalse
    End With
    
    ' Add mock field representation
    Set shp = slide.Shapes.AddRectangle(80, 170, 340, 180)
    With shp
        .Fill.ForeColor.RGB = RGB(34, 197, 94)
        .Fill.Transparency = 0.7
        .Line.ForeColor.RGB = RGB(255, 255, 255)
        .Line.Weight = 2
    End With
    
    ' Add heat zones (circles)
    Dim heatZones(4, 2) As Integer
    heatZones(0, 0) = 120: heatZones(0, 1) = 200: heatZones(0, 2) = 20
    heatZones(1, 0) = 180: heatZones(1, 1) = 230: heatZones(1, 2) = 15
    heatZones(2, 0) = 250: heatZones(2, 1) = 210: heatZones(2, 2) = 25
    heatZones(3, 0) = 320: heatZones(3, 1) = 280: heatZones(3, 2) = 18
    heatZones(4, 0) = 360: heatZones(4, 1) = 240: heatZones(4, 2) = 22
    
    For i = 0 To 4
        Set shp = slide.Shapes.AddShape(msoShapeOval, heatZones(i, 0), heatZones(i, 1), heatZones(i, 2), heatZones(i, 2))
        With shp
            .Fill.ForeColor.RGB = RGB(239, 68, 68)
            .Fill.Transparency = 0.3
            .Line.Visible = msoFalse
        End With
    Next i
    
    ' Stats panel
    Set shp = slide.Shapes.AddRoundedRectangle(500, 120, 400, 350)
    With shp
        .Fill.ForeColor.RGB = RGB(30, 41, 59)
        .Line.ForeColor.RGB = RGB(59, 130, 246)
        .Line.Weight = 2
        .TextFrame.TextRange.Text = "📊 PLAYER #10 STATISTICS" & vbCrLf & vbCrLf & _
            "🏃‍♂️ Distance Covered: 8.2 km" & vbCrLf & _
            "⚡ Max Speed: 24.3 km/h" & vbCrLf & _
            "🚀 Sprint Count: 18" & vbCrLf & _
            "⚽ Ball Touches: 64" & vbCrLf & _
            "⏱️ Possession Time: 4:23" & vbCrLf & _
            "🎯 Pass Accuracy: 87%" & vbCrLf & _
            "📈 Successful Passes: 38/44" & vbCrLf & _
            "🥅 Shots Taken: 3" & vbCrLf & _
            "⚽ Goals: 1" & vbCrLf & _
            "🛡️ Tackles: 5" & vbCrLf & _
            "🔄 Ball Recoveries: 7" & vbCrLf & vbCrLf & _
            "💪 Stamina Level: 82%" & vbCrLf & _
            "📍 Avg Position: Attacking Mid" & vbCrLf & _
            "🎯 Key Passes: 4"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 14
        .TextFrame.TextRange.Font.Color.RGB = RGB(248, 250, 252)
        .TextFrame.MarginLeft = 20
        .TextFrame.MarginTop = 20
    End With
End Sub

Sub CreateTeamAnalyticsSlide(slide As PowerPoint.Slide)
    Dim shp As PowerPoint.Shape
    
    slide.Background.Fill.ForeColor.RGB = RGB(15, 23, 42)
    
    ' Title
    Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 50, 30, 850, 60)
    With shp
        .TextFrame.TextRange.Text = "⚽ TEAM TACTICAL ANALYTICS"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 36
        .TextFrame.TextRange.Font.Bold = True
        .TextFrame.TextRange.Font.Color.RGB = RGB(59, 130, 246)
        .Fill.Visible = msoFalse
        .Line.Visible = msoFalse
    End With
    
    ' Possession chart
    Set shp = slide.Shapes.AddRoundedRectangle(50, 120, 200, 150)
    With shp
        .Fill.ForeColor.RGB = RGB(30, 41, 59)
        .Line.ForeColor.RGB = RGB(16, 185, 129)
        .Line.Weight = 2
    End With
    
    ' Possession pie chart representation
    Set shp = slide.Shapes.AddShape(msoShapeOval, 80, 140, 100, 100)
    With shp
        .Fill.ForeColor.RGB = RGB(59, 130, 246)
        .Line.Visible = msoFalse
    End With
    
    Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 70, 180, 120, 40)
    With shp
        .TextFrame.TextRange.Text = "POSSESSION" & vbCrLf & "Home: 58%" & vbCrLf & "Away: 42%"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 12
        .TextFrame.TextRange.Font.Bold = True
        .TextFrame.TextRange.Font.Color.RGB = RGB(248, 250, 252)
        .TextFrame.TextRange.ParagraphFormat.Alignment = ppAlignCenter
        .Fill.Visible = msoFalse
        .Line.Visible = msoFalse
    End With
    
    ' Pass network diagram
    Set shp = slide.Shapes.AddRoundedRectangle(280, 120, 300, 200)
    With shp
        .Fill.ForeColor.RGB = RGB(30, 41, 59)
        .Line.ForeColor.RGB = RGB(245, 158, 11)
        .Line.Weight = 2
    End With
    
    Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 300, 135, 260, 30)
    With shp
        .TextFrame.TextRange.Text = "🔗 PASS NETWORK"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 16
        .TextFrame.TextRange.Font.Bold = True
        .TextFrame.TextRange.Font.Color.RGB = RGB(245, 158, 11)
        .Fill.Visible = msoFalse
        .Line.Visible = msoFalse
    End With
    
    ' Add network nodes and connections
    Dim nodes(10, 2) As Integer
    For i = 0 To 10
        nodes(i, 0) = 320 + (i Mod 4) * 60
        nodes(i, 1) = 170 + (i \ 4) * 40
        
        Set shp = slide.Shapes.AddShape(msoShapeOval, nodes(i, 0), nodes(i, 1), 15, 15)
        With shp
            .Fill.ForeColor.RGB = RGB(59, 130, 246)
            .Line.Visible = msoFalse
        End With
        
        ' Add connections between some nodes
        If i > 0 And i Mod 3 = 0 Then
            Set shp = slide.Shapes.AddConnector(msoConnectorStraight, nodes(i - 1, 0) + 7, nodes(i - 1, 1) + 7, nodes(i, 0) + 7, nodes(i, 1) + 7)
            With shp
                .Line.ForeColor.RGB = RGB(16, 185, 129)
                .Line.Weight = 2
                .Line.Transparency = 0.3
            End With
        End If
    Next i
    
    ' Team stats
    Set shp = slide.Shapes.AddRoundedRectangle(620, 120, 280, 350)
    With shp
        .Fill.ForeColor.RGB = RGB(30, 41, 59)
        .Line.ForeColor.RGB = RGB(168, 85, 247)
        .Line.Weight = 2
        .TextFrame.TextRange.Text = "📈 TEAM STATISTICS" & vbCrLf & vbCrLf & _
            "🎯 Total Passes: 487" & vbCrLf & _
            "✅ Pass Accuracy: 84%" & vbCrLf & _
            "🚀 Attacking Third Entries: 23" & vbCrLf & _
            "🔄 Switches of Play: 12" & vbCrLf & _
            "🛡️ Ball Recoveries: 34" & vbCrLf & _
            "⚡ Press Intensity: 8.2" & vbCrLf & _
            "🏃‍♂️ Avg Sprint Speed: 19.4 km/h" & vbCrLf & _
            "📊 Formation: 4-3-3" & vbCrLf & _
            "📍 Team Compactness: 78%" & vbCrLf & vbCrLf & _
            "TACTICAL INSIGHTS:" & vbCrLf & _
            "• High pressing in final third" & vbCrLf & _
            "• Left-wing dominance" & vbCrLf & _
            "• Strong central midfield control" & vbCrLf & _
            "• Quick transition play"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 12
        .TextFrame.TextRange.Font.Color.RGB = RGB(248, 250, 252)
        .TextFrame.MarginLeft = 15
        .TextFrame.MarginTop = 15
    End With
    
    ' Formation visualization
    Set shp = slide.Shapes.AddRoundedRectangle(50, 300, 250, 170)
    With shp
        .Fill.ForeColor.RGB = RGB(30, 41, 59)
        .Line.ForeColor.RGB = RGB(236, 72, 153)
        .Line.Weight = 2
    End With
    
    Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 70, 315, 210, 30)
    With shp
        .TextFrame.TextRange.Text = "⚽ FORMATION: 4-3-3"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 14
        .TextFrame.TextRange.Font.Bold = True
        .TextFrame.TextRange.Font.Color.RGB = RGB(236, 72, 153)
        .Fill.Visible = msoFalse
        .Line.Visible = msoFalse
    End With
    
    ' Mini field with player positions
    Set shp = slide.Shapes.AddRectangle(80, 350, 190, 100)
    With shp
        .Fill.ForeColor.RGB = RGB(34, 197, 94)
        .Fill.Transparency = 0.7
        .Line.ForeColor.RGB = RGB(255, 255, 255)
        .Line.Weight = 1
    End With
    
    ' Add player position dots
    Dim positions(10, 1) As Integer
    positions(0, 0) = 90: positions(0, 1) = 400  ' GK
    positions(1, 0) = 120: positions(1, 1) = 380 ' DEF
    positions(2, 0) = 120: positions(2, 1) = 400
    positions(3, 0) = 120: positions(3, 1) = 420
    positions(4, 0) = 120: positions(4, 1) = 440
    positions(5, 0) = 160: positions(5, 1) = 390 ' MID
    positions(6, 0) = 160: positions(6, 1) = 410
    positions(7, 0) = 160: positions(7, 1) = 430
    positions(8, 0) = 200: positions(8, 1) = 385 ' FWD
    positions(9, 0) = 200: positions(9, 1) = 415
    positions(10, 0) = 200: positions(10, 1) = 435
    
    For i = 0 To 10
        Set shp = slide.Shapes.AddShape(msoShapeOval, positions(i, 0), positions(i, 1), 8, 8)
        With shp
            .Fill.ForeColor.RGB = RGB(59, 130, 246)
            .Line.ForeColor.RGB = RGB(255, 255, 255)
            .Line.Weight = 1
        End With
    Next i
End Sub

Sub CreateProcessingFlowSlide(slide As PowerPoint.Slide)
    Dim shp As PowerPoint.Shape
    
    slide.Background.Fill.ForeColor.RGB = RGB(15, 23, 42)
    
    ' Title
    Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 50, 30, 850, 60)
    With shp
        .TextFrame.TextRange.Text = "⚡ REAL-TIME PROCESSING FLOW"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 36
        .TextFrame.TextRange.Font.Bold = True
        .TextFrame.TextRange.Font.Color.RGB = RGB(59, 130, 246)
        .Fill.Visible = msoFalse
        .Line.Visible = msoFalse
    End With
    
    ' Processing components
    Dim components(7, 3) As String
    components(0, 0) = "📱 Video Input": components(0, 1) = "1080p@30fps": components(0, 2) = "H.264 Decode": components(0, 3) = RGB(16, 185, 129)
    components(1, 0) = "🔍 Frame Buffer": components(1, 1) = "Ring Buffer": components(1, 2) = "Memory Pool": components(1, 3) = RGB(59, 130, 246)
    components(2, 0) = "🎯 YOLO Inference": components(2, 1) = "GPU Accel": components(2, 2) = "Batch Process": components(2, 3) = RGB(245, 158, 11)
    components(3, 0) = "🧭 Object Tracker": components(3, 1) = "ByteTrack": components(3, 2) = "ID Persistence": components(3, 3) = RGB(168, 85, 247)
    components(4, 0) = "📐 Field Mapper": components(4, 1) = "Homography": components(4, 2) = "Coord Transform": components(4, 3) = RGB(236, 72, 153)
    components(5, 0) = "📊 Analytics Engine": components(5, 1) = "Real-time Stats": components(5, 2) = "Event Detection": components(5, 3) = RGB(34, 197, 94)
    components(6, 0) = "💾 Data Buffer": components(6, 1) = "Time Series": components(6, 2) = "Redis Cache": components(6, 3) = RGB(249, 115, 22)
    components(7, 0) = "📺 Live Dashboard": components(7, 1) = "WebSocket": components(7, 2) = "React UI": components(7, 3) = RGB(99, 102, 241)
    
    ' Create flow diagram
    For i = 0 To 7
        Dim xPos As Integer
        Dim yPos As Integer
        xPos = 50 + (i Mod 4) * 230
        yPos = 120 + (i \ 4) * 150
        
        ' Component box
        Set shp = slide.Shapes.AddRoundedRectangle(xPos, yPos, 200, 100)
        With shp
            .Fill.ForeColor.RGB = RGB(30, 41, 59)
            .Line.ForeColor.RGB = components(i, 3)
            .Line.Weight = 3
        End With
        
        ' Component title
        Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, xPos + 10, yPos + 10, 180, 25)
        With shp
            .TextFrame.TextRange.Text = components(i, 0)
            .TextFrame.TextRange.Font.Name = "Segoe UI"
            .TextFrame.TextRange.Font.Size = 14
            .TextFrame.TextRange.Font.Bold = True
            .TextFrame.TextRange.Font.Color.RGB = components(i, 3)
            .Fill.Visible = msoFalse
            .Line.Visible = msoFalse
        End With
        
        ' Component details
        Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, xPos + 10, yPos + 40, 180, 50)
        With shp
            .TextFrame.TextRange.Text = components(i, 1) & vbCrLf & components(i, 2)
            .TextFrame.TextRange.Font.Name = "Segoe UI"
            .TextFrame.TextRange.Font.Size = 11
            .TextFrame.TextRange.Font.Color.RGB = RGB(248, 250, 252)
            .Fill.Visible = msoFalse
            .Line.Visible = msoFalse
        End With
        
        ' Add arrows between components
        If i < 3 Then ' First row horizontal arrows
            Set shp = slide.Shapes.AddConnector(msoConnectorStraight, xPos + 200, yPos + 50, xPos + 230, yPos + 50)
            With shp
                .Line.ForeColor.RGB = RGB(59, 130, 246)
                .Line.Weight = 3
                .Line.EndArrowheadStyle = msoArrowheadTriangle
            End With
        ElseIf i = 3 Then ' Vertical arrow from row 1 to row 2
            Set shp = slide.Shapes.AddConnector(msoConnectorElbow, 740, 170, 280, 270)
            With shp
                .Line.ForeColor.RGB = RGB(59, 130, 246)
                .Line.Weight = 3
                .Line.EndArrowheadStyle = msoArrowheadTriangle
            End With
        ElseIf i > 3 And i < 7 Then ' Second row horizontal arrows
            Set shp = slide.Shapes.AddConnector(msoConnectorStraight, xPos + 200, yPos + 50, xPos + 230, yPos + 50)
            With shp
                .Line.ForeColor.RGB = RGB(59, 130, 246)
                .Line.Weight = 3
                .Line.EndArrowheadStyle = msoArrowheadTriangle
            End With
        End If
    Next i
    
    ' Performance metrics box
    Set shp = slide.Shapes.AddRoundedRectangle(50, 420, 850, 80)
    With shp
        .Fill.ForeColor.RGB = RGB(30, 41, 59)
        .Line.ForeColor.RGB = RGB(34, 197, 94)
        .Line.Weight = 2
        .TextFrame.TextRange.Text = "⚡ PERFORMANCE METRICS: " & _
            "Latency: <100ms | Throughput: 30 FPS | Memory: <2GB RAM | " & _
            "GPU Utilization: 65% | CPU Usage: 45% | Accuracy: 94.2%"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 16
        .TextFrame.TextRange.Font.Bold = True
        .TextFrame.TextRange.Font.Color.RGB = RGB(34, 197, 94)
        .TextFrame.TextRange.ParagraphFormat.Alignment = ppAlignCenter
        .TextFrame.MarginTop = 25
    End With
End Sub

Sub CreateDeploymentSlide(slide As PowerPoint.Slide)
    Dim shp As PowerPoint.Shape
    
    slide.Background.Fill.ForeColor.RGB = RGB(15, 23, 42)
    
    ' Title
    Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 50, 30, 850, 60)
    With shp
        .TextFrame.TextRange.Text = "☁️ DEPLOYMENT ARCHITECTURE"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 36
        .TextFrame.TextRange.Font.Bold = True
        .TextFrame.TextRange.Font.Color.RGB = RGB(59, 130, 246)
        .Fill.Visible = msoFalse
        .Line.Visible = msoFalse
    End With
    
    ' Cloud infrastructure
    Set shp = slide.Shapes.AddRoundedRectangle(50, 120, 400, 300)
    With shp
        .Fill.ForeColor.RGB = RGB(30, 41, 59)
        .Line.ForeColor.RGB = RGB(59, 130, 246)
        .Line.Weight = 3
        .Line.DashStyle = msoLineDash
    End With
    
    Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 70, 135, 360, 30)
    With shp
        .TextFrame.TextRange.Text = "☁️ AWS CLOUD INFRASTRUCTURE"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 18
        .TextFrame.TextRange.Font.Bold = True
        .TextFrame.TextRange.Font.Color.RGB = RGB(59, 130, 246)
        .Fill.Visible = msoFalse
        .Line.Visible = msoFalse
    End With
    
    ' AWS services
    Dim awsServices(4, 1) As String
    awsServices(0, 0) = "EC2 GPU Instance": awsServices(0, 1) = "g4dn.xlarge"
    awsServices(1, 0) = "S3 Storage": awsServices(1, 1) = "Video + Models"
    awsServices(2, 0) = "Lambda Functions": awsServices(2, 1) = "Event Processing"
    awsServices(3, 0) = "CloudFront CDN": awsServices(3, 1) = "Global Distribution"
    awsServices(4, 0) = "RDS Database": awsServices(4, 1) = "Match Data"
    
    For i = 0 To 4
        Set shp = slide.Shapes.AddRoundedRectangle(70 + (i Mod 2) * 160, 180 + (i \ 2) * 60, 140, 45)
        With shp
            .Fill.ForeColor.RGB = RGB(255, 153, 0)
            .Line.Visible = msoFalse
            .TextFrame.TextRange.Text = awsServices(i, 0) & vbCrLf & awsServices(i, 1)
            .TextFrame.TextRange.Font.Name = "Segoe UI"
            .TextFrame.TextRange.Font.Size = 10
            .TextFrame.TextRange.Font.Bold = True
            .TextFrame.TextRange.Font.Color.RGB = RGB(0, 0, 0)
            .TextFrame.TextRange.ParagraphFormat.Alignment = ppAlignCenter
        End With
    Next i
    
    ' Edge deployment
    Set shp = slide.Shapes.AddRoundedRectangle(500, 120, 400, 300)
    With shp
        .Fill.ForeColor.RGB = RGB(30, 41, 59)
        .Line.ForeColor.RGB = RGB(16, 185, 129)
        .Line.Weight = 3
        .Line.DashStyle = msoLineDash
    End With
    
    Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 520, 135, 360, 30)
    With shp
        .TextFrame.TextRange.Text = "📱 EDGE DEPLOYMENT"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 18
        .TextFrame.TextRange.Font.Bold = True
        .TextFrame.TextRange.Font.Color.RGB = RGB(16, 185, 129)
        .Fill.Visible = msoFalse
        .Line.Visible = msoFalse
    End With
    
    ' Edge components
    Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 520, 180, 360, 220)
    With shp
        .TextFrame.TextRange.Text = "🔧 OPTIMIZATION STACK:" & vbCrLf & vbCrLf & _
            "• ONNX Runtime for cross-platform inference" & vbCrLf & _
            "• TensorRT for NVIDIA GPU acceleration" & vbCrLf & _
            "• TorchScript for mobile deployment" & vbCrLf & _
            "• Model quantization (FP16/INT8)" & vbCrLf & _
            "• Dynamic batching for efficiency" & vbCrLf & vbCrLf & _
            "📊 EDGE PERFORMANCE:" & vbCrLf & _
            "• Jetson Xavier NX: 15 FPS" & vbCrLf & _
            "• RTX 3060 Laptop: 45 FPS" & vbCrLf & _
            "• Apple M1 Pro: 25 FPS" & vbCrLf & _
            "• Mobile (Snapdragon): 8 FPS"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 12
        .TextFrame.TextRange.Font.Color.RGB = RGB(248, 250, 252)
        .Fill.Visible = msoFalse
        .Line.Visible = msoFalse
    End With
    
    ' Docker container
    Set shp = slide.Shapes.AddRoundedRectangle(200, 450, 500, 50)
    With shp
        .Fill.ForeColor.RGB = RGB(37, 99, 235)
        .Line.Visible = msoFalse
        .TextFrame.TextRange.Text = "🐳 DOCKER CONTAINERIZATION: Multi-stage builds | GPU support | Kubernetes ready"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 14
        .TextFrame.TextRange.Font.Bold = True
        .TextFrame.TextRange.Font.Color.RGB = RGB(255, 255, 255)
        .TextFrame.TextRange.ParagraphFormat.Alignment = ppAlignCenter
        .TextFrame.MarginTop = 15
    End With
End Sub

Sub CreateRoadmapSlide(slide As PowerPoint.Slide)
    Dim shp As PowerPoint.Shape
    
    slide.Background.Fill.ForeColor.RGB = RGB(15, 23, 42)
    
    ' Title
    Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 50, 30, 850, 60)
    With shp
        .TextFrame.TextRange.Text = "🚀 FUTURE ROADMAP & DELIVERABLES"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 36
        .TextFrame.TextRange.Font.Bold = True
        .TextFrame.TextRange.Font.Color.RGB = RGB(59, 130, 246)
        .Fill.Visible = msoFalse
        .Line.Visible = msoFalse
    End With
    
    ' Current deliverables
    Set shp = slide.Shapes.AddRoundedRectangle(50, 120, 400, 200)
    With shp
        .Fill.ForeColor.RGB = RGB(30, 41, 59)
        .Line.ForeColor.RGB = RGB(16, 185, 129)
        .Line.Weight = 3
        .TextFrame.TextRange.Text = "📦 WEEK 10 DELIVERABLES" & vbCrLf & vbCrLf & _
            "✅ Fully working analytics pipeline" & vbCrLf & _
            "✅ Web UI for uploads + results" & vbCrLf & _
            "✅ Visual reports (CSV, PDF, JSON)" & vbCrLf & _
            "✅ GitHub repo + documentation" & vbCrLf & _
            "✅ Setup instructions & tutorials" & vbCrLf & _
            "✅ Screen-recorded demo" & vbCrLf & _
            "✅ Performance benchmarks" & vbCrLf & _
            "✅ API documentation"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 14
        .TextFrame.TextRange.Font.Color.RGB = RGB(248, 250, 252)
        .TextFrame.MarginLeft = 20
        .TextFrame.MarginTop = 20
    End With
    
    ' Future features
    Set shp = slide.Shapes.AddRoundedRectangle(500, 120, 400, 200)
    With shp
        .Fill.ForeColor.RGB = RGB(30, 41, 59)
        .Line.ForeColor.RGB = RGB(245, 158, 11)
        .Line.Weight = 3
        .TextFrame.TextRange.Text = "🔮 V2.0 FUTURE FEATURES" & vbCrLf & vbCrLf & _
            "🔁 Advanced Pose Estimation" & vbCrLf & _
            "🤝 Team Shape Analysis" & vbCrLf & _
            "🌐 Multi-Camera Fusion" & vbCrLf & _
            "📲 Mobile App (iOS/Android)" & vbCrLf & _
            "⏱️ Real-Time Overlay Graphics" & vbCrLf & _
            "🤖 AI Commentary Generation" & vbCrLf & _
            "📈 Predictive Analytics" & vbCrLf & _
            "🎮 VR/AR Match Replay"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 14
        .TextFrame.TextRange.Font.Color.RGB = RGB(248, 250, 252)
        .TextFrame.MarginLeft = 20
        .TextFrame.MarginTop = 20
    End With
    
    ' Technical achievements
    Set shp = slide.Shapes.AddRoundedRectangle(50, 350, 400, 120)
    With shp
        .Fill.ForeColor.RGB = RGB(30, 41, 59)
        .Line.ForeColor.RGB = RGB(168, 85, 247)
        .Line.Weight = 3
        .TextFrame.TextRange.Text = "🏆 TECHNICAL ACHIEVEMENTS" & vbCrLf & vbCrLf & _
            "• 94.2% object detection accuracy" & vbCrLf & _
            "• <100ms end-to-end latency" & vbCrLf & _
            "• Real-time 30 FPS processing" & vbCrLf & _
            "• Cross-platform deployment"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 14
        .TextFrame.TextRange.Font.Color.RGB = RGB(248, 250, 252)
        .TextFrame.MarginLeft = 20
        .TextFrame.MarginTop = 20
    End With
    
    ' Market impact
    Set shp = slide.Shapes.AddRoundedRectangle(500, 350, 400, 120)
    With shp
        .Fill.ForeColor.RGB = RGB(30, 41, 59)
        .Line.ForeColor.RGB = RGB(236, 72, 153)
        .Line.Weight = 3
        .TextFrame.TextRange.Text = "💼 MARKET IMPACT" & vbCrLf & vbCrLf & _
            "• Democratizes professional analytics" & vbCrLf & _
            "• Reduces cost by 90% vs traditional" & vbCrLf & _
            "• Accessible to grassroots football" & vbCrLf & _
            "• Scalable to any video source"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 14
        .TextFrame.TextRange.Font.Color.RGB = RGB(248, 250, 252)
        .TextFrame.MarginLeft = 20
        .TextFrame.MarginTop = 20
    End With
End Sub

Sub AddTechIcons(slide As PowerPoint.Slide)
    ' Add decorative tech icons around the title slide
    Dim icons(5, 2) As String
    icons(0, 0) = "🎯": icons(0, 1) = 150: icons(0, 2) = 100
    icons(1, 0) = "🧠": icons(1, 1) = 800: icons(1, 2) = 120
    icons(2, 0) = "⚡": icons(2, 1) = 100: icons(2, 2) = 300
    icons(3, 0) = "📊": icons(3, 1) = 850: icons(3, 2) = 280
    icons(4, 0) = "🚀": icons(4, 1) = 120: icons(4, 2) = 480
    icons(5, 0) = "💎": icons(5, 1) = 820: icons(5, 2) = 450
    
    For i = 0 To 5
        Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, icons(i, 1), icons(i, 2), 40, 40)
        With shp
            .TextFrame.TextRange.Text = icons(i, 0)
            .TextFrame.TextRange.Font.Size = 32
            .Fill.Visible = msoFalse
            .Line.Visible = msoFalse
            .AnimationSettings.EntryEffect = ppEffectFadedSwivel
            .AnimationSettings.AfterEffect = ppAfterEffectHideAfterNextClick
        End With
    Next i
End Sub

Sub ApplyTransitions(pres As PowerPoint.Presentation)
    Dim slide As PowerPoint.slide
    Dim i As Integer
    
    ' Apply different transitions to each slide
    For i = 1 To pres.Slides.Count
        Set slide = pres.Slides(i)
        
        Select Case i
            Case 1 ' Title slide - Fade transition
                With slide.SlideShowTransition
                    .EntryEffect = ppEffectFade
                    .Duration = 1
                End With
                
            Case 2 ' Overview - Push transition
                With slide.SlideShowTransition
                    .EntryEffect = ppEffectPushLeft
                    .Duration = 0.8
                End With
                
            Case 3 ' Tech Stack - Wipe transition
                With slide.SlideShowTransition
                    .EntryEffect = ppEffectWipeRight
                    .Duration = 0.7
                End With
                
            Case 4 ' ML Pipeline - Conveyor transition
                With slide.SlideShowTransition
                    .EntryEffect = ppEffectConveyorLeft
                    .Duration = 1.2
                End With
                
            Case 5 ' Timeline - Gallery transition
                With slide.SlideShowTransition
                    .EntryEffect = ppEffectGalleryLeft
                    .Duration = 1
                End With
                
            Case 6 ' Features Matrix - Cube transition
                With slide.SlideShowTransition
                    .EntryEffect = ppEffectCubeLeft
                    .Duration = 1.5
                End With
                
            Case 7 ' Player Analytics - Morph transition
                With slide.SlideShowTransition
                    .EntryEffect = ppEffectMorphByObject
                    .Duration = 1.8
                End With
                
            Case 8 ' Team Analytics - Reveal transition
                With slide.SlideShowTransition
                    .EntryEffect = ppEffectRevealBlackLeft
                    .Duration = 1
                End With
                
            Case 9 ' Processing Flow - Ripple transition
                With slide.SlideShowTransition
                    .EntryEffect = ppEffectRippleLeftDown
                    .Duration = 1.3
                End With
                
            Case 10 ' Deployment - Honeycomb transition
                With slide.SlideShowTransition
                    .EntryEffect = ppEffectHoneycomb
                    .Duration = 1.5
                End With
                
            Case 11 ' Roadmap - Shred transition
                With slide.SlideShowTransition
                    .EntryEffect = ppEffectShredRectangleIn
                    .Duration = 1.2
                End With
        End Select
        
        ' Add entrance animations to key elements
        Call AddSlideAnimations(slide, i)
    Next i
End Sub

Sub AddSlideAnimations(slide As PowerPoint.slide, slideNum As Integer)
    Dim shp As PowerPoint.Shape
    Dim animEffect As PowerPoint.Effect
    
    ' Add entrance animations based on slide type
    Select Case slideNum
        Case 1 ' Title slide animations
            For Each shp In slide.Shapes
                If shp.HasTextFrame Then
                    If InStr(shp.TextFrame.TextRange.Text, "AI-POWERED") > 0 Then
                        ' Main title - Fly in from left
                        shp.AnimationSettings.EntryEffect = ppEffectFlyFromLeft
                        shp.AnimationSettings.AnimateTextInReverse = msoFalse
                        shp.AnimationSettings.TextLevelEffect = ppAnimateByAllLevels
                    ElseIf InStr(shp.TextFrame.TextRange.Text, "Computer Vision") > 0 Then
                        ' Subtitle - Fade in
                        shp.AnimationSettings.EntryEffect = ppEffectAppear
                        shp.AnimationSettings.AnimationOrder = 2
                    End If
                End If
            Next shp
            
        Case 3 ' Tech stack - Animate boxes sequentially
            Dim animOrder As Integer
            animOrder = 1
            For Each shp In slide.Shapes
                If shp.Type = msoAutoShape And shp.AutoShapeType = msoShapeRoundedRectangle Then
                    shp.AnimationSettings.EntryEffect = ppEffectBoxIn
                    shp.AnimationSettings.AnimationOrder = animOrder
                    animOrder = animOrder + 1
                End If
            Next shp
            
        Case 5 ' Timeline - Animate week by week
            animOrder = 1
            For Each shp In slide.Shapes
                If shp.Type = msoAutoShape And shp.AutoShapeType = msoShapeOval Then
                    shp.AnimationSettings.EntryEffect = ppEffectBounce
                    shp.AnimationSettings.AnimationOrder = animOrder
                    animOrder = animOrder + 1
                End If
            Next shp
            
        Case 7, 8 ' Analytics slides - Emphasize key metrics
            For Each shp In slide.Shapes
                If shp.HasTextFrame Then
                    If InStr(shp.TextFrame.TextRange.Text, "STATISTICS") > 0 Or _
                       InStr(shp.TextFrame.TextRange.Text, "ANALYTICS") > 0 Then
                        shp.AnimationSettings.EntryEffect = ppEffectPeek
                        shp.AnimationSettings.AnimateTextInReverse = msoFalse
                    End If
                End If
            Next shp
            
        Case 9 ' Processing flow - Chain animation
            animOrder = 1
            For Each shp In slide.Shapes
                If shp.Type = msoAutoShape And shp.AutoShapeType = msoShapeRoundedRectangle Then
                    shp.AnimationSettings.EntryEffect = ppEffectWipeRight
                    shp.AnimationSettings.AnimationOrder = animOrder
                    animOrder = animOrder + 1
                End If
            Next shp
    End Select
End Sub

' Helper function to create gradient backgrounds
Sub CreateGradientBackground(slide As PowerPoint.slide, color1 As Long, color2 As Long)
    With slide.Background.Fill
        .Visible = msoTrue
        .ForeColor.RGB = color1
        .BackColor.RGB = color2
        .GradientColorType = msoGradientTwoColors
        .GradientStyle = msoGradientDiagonalDown
        .GradientVariant = 1
    End With
End Sub

' Function to add professional shadows to shapes
Sub AddProfessionalShadow(shp As PowerPoint.Shape)
    With shp.Shadow
        .Type = msoShadowOffset
        .OffsetX = 4
        .OffsetY = 4
        .Blur = 8
        .ForeColor.RGB = RGB(0, 0, 0)
        .Transparency = 0.3
    End With
End Sub

' Function to create animated progress bars for metrics
Sub CreateProgressBar(slide As PowerPoint.slide, xPos As Integer, yPos As Integer, _
                     width As Integer, percentage As Integer, label As String)
    Dim bgBar As PowerPoint.Shape
    Dim progressBar As PowerPoint.Shape
    Dim labelShape As PowerPoint.Shape
    
    ' Background bar
    Set bgBar = slide.Shapes.AddRectangle(xPos, yPos, width, 20)
    With bgBar
        .Fill.ForeColor.RGB = RGB(55, 65, 81)
        .Line.Visible = msoFalse
    End With
    
    ' Progress bar
    Set progressBar = slide.Shapes.AddRectangle(xPos, yPos, width * percentage / 100, 20)
    With progressBar
        .Fill.ForeColor.RGB = RGB(34, 197, 94)
        .Line.Visible = msoFalse
        .AnimationSettings.EntryEffect = ppEffectWipeRight
        .AnimationSettings.AnimationOrder = 2
    End With
    
    ' Label
    Set labelShape = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, xPos, yPos - 25, width, 20)
    With labelShape
        .TextFrame.TextRange.Text = label & " (" & percentage & "%)"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 12
        .TextFrame.TextRange.Font.Color.RGB = RGB(248, 250, 252)
        .Fill.Visible = msoFalse
        .Line.Visible = msoFalse
    End With
End Sub = 20
    End With
    
    ' Key Features box
    Set shp = slide.Shapes.AddRoundedRectangle(500, 120, 400, 350)
    With shp
        .Fill.ForeColor.RGB = RGB(30, 41, 59)
        .Line.ForeColor.RGB = RGB(16, 185, 129)
        .Line.Weight = 2
        .TextFrame.TextRange.Text = "✨ KEY FEATURES" & vbCrLf & vbCrLf & _
            "• Real-time Player Tracking" & vbCrLf & _
            "• Advanced Ball Detection" & vbCrLf & _
            "• FIFA-style Heatmaps" & vbCrLf & _
            "• Tactical Analysis Engine" & vbCrLf & _
            "• Possession & Pass Networks" & vbCrLf & _
            "• Auto Event Detection" & vbCrLf & _
            "• Interactive Dashboards" & vbCrLf & _
            "• Gimbal Integration" & vbCrLf & _
            "• Edge Deployment Ready"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 14
        .TextFrame.TextRange.Font.Color.RGB = RGB(248, 250, 252)
        .TextFrame.MarginLeft = 20
        .TextFrame.MarginTop = 20
    End With
    
    ' Technology Approach box
    Set shp = slide.Shapes.AddRoundedRectangle(50, 300, 400, 170)
    With shp
        .Fill.ForeColor.RGB = RGB(30, 41, 59)
        .Line.ForeColor.RGB = RGB(245, 158, 11)
        .Line.Weight = 2
        .TextFrame.TextRange.Text = "🧠 APPROACH" & vbCrLf & vbCrLf & _
            "• YOLOv8 for Object Detection" & vbCrLf & _
            "• ByteTrack for Multi-Object Tracking" & vbCrLf & _
            "• Computer Vision for Field Mapping" & vbCrLf & _
            "• ML-based Event Classification"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 14
        .TextFrame.TextRange.Font.Color.RGB = RGB(248, 250, 252)
        .TextFrame.MarginLeft = 20
        .TextFrame.MarginTop = 20
    End With
End Sub

Sub CreateTechStackSlide(slide As PowerPoint.Slide)
    Dim shp As PowerPoint.Shape
    Dim i As Integer
    
    slide.Background.Fill.ForeColor.RGB = RGB(15, 23, 42)
    
    ' Title
    Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 50, 30, 850, 60)
    With shp
        .TextFrame.TextRange.Text = "🧰 ENTERPRISE TECH STACK"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 36
        .TextFrame.TextRange.Font.Bold = True
        .TextFrame.TextRange.Font.Color.RGB = RGB(59, 130, 246)
        .Fill.Visible = msoFalse
        .Line.Visible = msoFalse
    End With
    
    ' Create tech stack categories
    Dim categories(7, 2) As String
    categories(0, 0) = "Object Detection"
    categories(0, 1) = "YOLOv8 (Ultralytics)" & vbCrLf & "TorchHub"
    categories(0, 2) = "🎯"
    
    categories(1, 0) = "Tracking"
    categories(1, 1) = "ByteTrack" & vbCrLf & "StrongSORT"
    categories(1, 2) = "🧭"
    
    categories(2, 0) = "Video Processing"
    categories(2, 1) = "OpenCV" & vbCrLf & "PyAV, FFmpeg"
    categories(2, 2) = "📹"
    
    categories(3, 0) = "Analytics Engine"
    categories(3, 1) = "Pandas, NumPy" & vbCrLf & "Scikit-learn"
    categories(3, 2) = "🧮"
    
    categories(4, 0) = "Visualization"
    categories(4, 1) = "Plotly, Seaborn" & vbCrLf & "OpenCV Drawing"
    categories(4, 2) = "📊"
    
    categories(5, 0) = "Web Interface"
    categories(5, 1) = "Streamlit (MVP)" & vbCrLf & "Next.js + Flask"
    categories(5, 2) = "🌐"
    
    categories(6, 0) = "Edge Inference"
    categories(6, 1) = "ONNX Runtime" & vbCrLf & "TensorRT"
    categories(6, 2) = "⚡"
    
    categories(7, 0) = "Deployment"
    categories(7, 1) = "Docker, AWS EC2" & vbCrLf & "S3 Storage"
    categories(7, 2) = "☁️"
    
    ' Create grid layout
    For i = 0 To 7
        Dim row As Integer
        Dim col As Integer
        row = i \ 4
        col = i Mod 4
        
        ' Create category box
        Set shp = slide.Shapes.AddRoundedRectangle(50 + col * 220, 120 + row * 180, 200, 150)
        With shp
            .Fill.ForeColor.RGB = RGB(30, 41, 59)
            .Line.ForeColor.RGB = RGB(59, 130, 246)
            .Line.Weight = 2
        End With
        
        ' Add icon
        Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 60 + col * 220, 130 + row * 180, 40, 30)
        With shp
            .TextFrame.TextRange.Text = categories(i, 2)
            .TextFrame.TextRange.Font.Size = 24
            .Fill.Visible = msoFalse
            .Line.Visible = msoFalse
        End With
        
        ' Add category title
        Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 110 + col * 220, 130 + row * 180, 130, 30)
        With shp
            .TextFrame.TextRange.Text = categories(i, 0)
            .TextFrame.TextRange.Font.Name = "Segoe UI"
            .TextFrame.TextRange.Font.Size = 14
            .TextFrame.TextRange.Font.Bold = True
            .TextFrame.TextRange.Font.Color.RGB = RGB(59, 130, 246)
            .Fill.Visible = msoFalse
            .Line.Visible = msoFalse
        End With
        
        ' Add tools
        Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 60 + col * 220, 165 + row * 180, 180, 90)
        With shp
            .TextFrame.TextRange.Text = categories(i, 1)
            .TextFrame.TextRange.Font.Name = "Segoe UI"
            .TextFrame.TextRange.Font.Size = 12
            .TextFrame.TextRange.Font.Color.RGB = RGB(248, 250, 252)
            .Fill.Visible = msoFalse
            .Line.Visible = msoFalse
        End With
    Next i
End Sub

Sub CreateMLPipelineSlide(slide As PowerPoint.Slide)
    Dim shp As PowerPoint.Shape
    
    slide.Background.Fill.ForeColor.RGB = RGB(15, 23, 42)
    
    ' Title
    Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 50, 30, 850, 60)
    With shp
        .TextFrame.TextRange.Text = "🧠 ML/CV PROCESSING PIPELINE"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 36
        .TextFrame.TextRange.Font.Bold = True
        .TextFrame.TextRange.Font.Color.RGB = RGB(59, 130, 246)
        .Fill.Visible = msoFalse
        .Line.Visible = msoFalse
    End With
    
    ' Pipeline stages
    Dim stages(5) As String
    stages(0) = "📱 Video Input"
    stages(1) = "🎯 YOLOv8 Detection"
    stages(2) = "🧭 Multi-Object Tracking"
    stages(3) = "📐 Field Calibration"
    stages(4) = "📊 Analytics Engine"
    stages(5) = "📋 Dashboard Output"
    
    ' Create pipeline flow
    For i = 0 To 5
        ' Stage box
        Set shp = slide.Shapes.AddRoundedRectangle(50, 120 + i * 60, 300, 50)
        With shp
            .Fill.ForeColor.RGB = RGB(30, 41, 59)
            .Line.ForeColor.RGB = RGB(16, 185, 129)
            .Line.Weight = 2
            .TextFrame.TextRange.Text = stages(i)
            .TextFrame.TextRange.Font.Name = "Segoe UI"
            .TextFrame.TextRange.Font.Size = 16
            .TextFrame.TextRange.Font.Bold = True
            .TextFrame.TextRange.Font.Color.RGB = RGB(248, 250, 252)
            .TextFrame.TextRange.ParagraphFormat.Alignment = ppAlignCenter
        End With
        
        ' Arrow (except for last stage)
        If i < 5 Then
            Set shp = slide.Shapes.AddConnector(msoConnectorStraight, 200, 120 + i * 60 + 50, 200, 120 + (i + 1) * 60)
            With shp
                .Line.ForeColor.RGB = RGB(59, 130, 246)
                .Line.Weight = 3
                .Line.EndArrowheadStyle = msoArrowheadTriangle
            End With
        End If
    Next i
    
    ' Technical details box
    Set shp = slide.Shapes.AddRoundedRectangle(400, 120, 450, 360)
    With shp
        .Fill.ForeColor.RGB = RGB(30, 41, 59)
        .Line.ForeColor.RGB = RGB(245, 158, 11)
        .Line.Weight = 2
        .TextFrame.TextRange.Text = "🔧 TECHNICAL DETAILS" & vbCrLf & vbCrLf & _
            "Detection Models:" & vbCrLf & _
            "• YOLOv8n/s/m for speed/accuracy trade-off" & vbCrLf & _
            "• Custom training on football datasets" & vbCrLf & _
            "• Real-time inference optimization" & vbCrLf & vbCrLf & _
            "Tracking Algorithm:" & vbCrLf & _
            "• ByteTrack for robust ID persistence" & vbCrLf & _
            "• Kalman filtering for smooth trajectories" & vbCrLf & _
            "• Occlusion handling" & vbCrLf & vbCrLf & _
            "Field Mapping:" & vbCrLf & _
            "• Homography transformation" & vbCrLf & _
            "• Perspective correction" & vbCrLf & _
            "• Real-world coordinate mapping" & vbCrLf & vbCrLf & _
            "Analytics:" & vbCrLf & _
            "• Distance & speed calculations" & vbCrLf & _
            "• Event detection algorithms" & vbCrLf & _
            "• Statistical analysis engine"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 12
        .TextFrame.TextRange.Font.Color.RGB = RGB(248, 250, 252)
        .TextFrame.MarginLeft = 20
        .TextFrame.MarginTop = 20
    End With
End Sub

Sub CreateTimelineSlide(slide As PowerPoint.Slide)
    Dim shp As PowerPoint.Shape
    
    slide.Background.Fill.ForeColor.RGB = RGB(15, 23, 42)
    
    ' Title
    Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 50, 30, 850, 60)
    With shp
        .TextFrame.TextRange.Text = "🗓️ 10-WEEK DEVELOPMENT TIMELINE"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 36
        .TextFrame.TextRange.Font.Bold = True
        .TextFrame.TextRange.Font.Color.RGB = RGB(59, 130, 246)
        .Fill.Visible = msoFalse
        .Line.Visible = msoFalse
    End With
    
    ' Timeline data
    Dim weeks(9, 2) As String
    weeks(0, 0) = "W1": weeks(0, 1) = "🔧 Setup + Baseline": weeks(0, 2) = "Project setup, YOLOv8 baseline"
    weeks(1, 0) = "W2": weeks(1, 1) = "🎯 Detection": weeks(1, 2) = "Player + Ball detection models"
    weeks(2, 0) = "W3": weeks(2, 1) = "🧭 Tracking": weeks(2, 2) = "ByteTrack integration, ID persistence"
    weeks(3, 0) = "W4": weeks(3, 1) = "🧮 Calibration": weeks(3, 2) = "Field mapping, homography tool"
    weeks(4, 0) = "W5": weeks(4, 1) = "📷 Gimbal SDK": weeks(4, 2) = "Auto-tracking prototype"
    weeks(5, 0) = "W6": weeks(5, 1) = "🧠 Player Stats": weeks(5, 2) = "Speed, distance, heatmaps"
    weeks(6, 0) = "W7": weeks(6, 1) = "📊 Tactical": weeks(6, 2) = "Possession, pass networks"
    weeks(7, 0) = "W8": weeks(7, 1) = "🎯 Events": weeks(7, 2) = "Goals, shots, tackles detection"
    weeks(8, 0) = "W9": weeks(8, 1) = "📋 Dashboard": weeks(8, 2) = "UI, reports, highlights"
    weeks(9, 0) = "W10": weeks(9, 1) = "🧼 Polish": weeks(9, 2) = "Optimization, docs, packaging"
    
    ' Create timeline
    For i = 0 To 9
        Dim xPos As Integer
        Dim yPos As Integer
        xPos = 50 + (i Mod 5) * 180
        yPos = 120 + (i \ 5) * 120
        
        ' Week circle
        Set shp = slide.Shapes.AddShape(msoShapeOval, xPos, yPos, 40, 40)
        With shp
            .Fill.ForeColor.RGB = RGB(59, 130, 246)
            .Line.Visible = msoFalse
            .TextFrame.TextRange.Text = weeks(i, 0)
            .TextFrame.TextRange.Font.Name = "Segoe UI"
            .TextFrame.TextRange.Font.Size = 12
            .TextFrame.TextRange.Font.Bold = True
            .TextFrame.TextRange.Font.Color.RGB = RGB(255, 255, 255)
            .TextFrame.TextRange.ParagraphFormat.Alignment = ppAlignCenter
        End With
        
        ' Week title
        Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, xPos + 50, yPos, 120, 20)
        With shp
            .TextFrame.TextRange.Text = weeks(i, 1)
            .TextFrame.TextRange.Font.Name = "Segoe UI"
            .TextFrame.TextRange.Font.Size = 14
            .TextFrame.TextRange.Font.Bold = True
            .TextFrame.TextRange.Font.Color.RGB = RGB(16, 185, 129)
            .Fill.Visible = msoFalse
            .Line.Visible = msoFalse
        End With
        
        ' Week description
        Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, xPos + 50, yPos + 25, 120, 35)
        With shp
            .TextFrame.TextRange.Text = weeks(i, 2)
            .TextFrame.TextRange.Font.Name = "Segoe UI"
            .TextFrame.TextRange.Font.Size = 10
            .TextFrame.TextRange.Font.Color.RGB = RGB(248, 250, 252)
            .Fill.Visible = msoFalse
            .Line.Visible = msoFalse
        End With
        
        ' Connect with arrows (horizontal connections)
        If i Mod 5 < 4 And i < 9 Then
            Set shp = slide.Shapes.AddConnector(msoConnectorStraight, xPos + 40, yPos + 20, xPos + 180, yPos + 20)
            With shp
                .Line.ForeColor.RGB = RGB(59, 130, 246)
                .Line.Weight = 2
                .Line.EndArrowheadStyle = msoArrowheadTriangle
            End With
        End If
        
        ' Vertical connection
        If i = 4 Then
            Set shp = slide.Shapes.AddConnector(msoConnectorElbow, xPos + 20, yPos + 40, 70, yPos + 120)
            With shp
                .Line.ForeColor.RGB = RGB(59, 130, 246)
                .Line.Weight = 2
                .Line.EndArrowheadStyle = msoArrowheadTriangle
            End With
        End If
    Next i
End Sub

Sub CreateFeaturesMatrixSlide(slide As PowerPoint.Slide)
    Dim shp As PowerPoint.Shape
    
    slide.Background.Fill.ForeColor.RGB = RGB(15, 23, 42)
    
    ' Title
    Set shp = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, 50, 30, 850, 60)
    With shp
        .TextFrame.TextRange.Text = "📊 ANALYTICS FEATURES MATRIX"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 36
        .TextFrame.TextRange.Font.Bold = True
        .TextFrame.TextRange.Font.Color.RGB = RGB(59, 130, 246)
        .Fill.Visible = msoFalse
        .Line.Visible = msoFalse
    End With
    
    ' Create three columns for different feature categories
    ' Player Analytics
    Set shp = slide.Shapes.AddRoundedRectangle(50, 120, 280, 350)
    With shp
        .Fill.ForeColor.RGB = RGB(30, 41, 59)
        .Line.ForeColor.RGB = RGB(16, 185, 129)
        .Line.Weight = 2
        .TextFrame.TextRange.Text = "✅ PLAYER ANALYTICS" & vbCrLf & vbCrLf & _
            "• Player Detection & Tracking" & vbCrLf & _
            "• Individual Heatmaps" & vbCrLf & _
            "• Distance Covered" & vbCrLf & _
            "• Speed & Sprint Analysis" & vbCrLf & _
            "• Stamina Estimation" & vbCrLf & _
            "• Ball Touches Count" & vbCrLf & _
            "• Time in Possession" & vbCrLf & _
            "• Pass Accuracy %" & vbCrLf & _
            "• Shots Taken" & vbCrLf & _
            "• Goals Scored" & vbCrLf & _
            "• Tackles Made"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 12
        .TextFrame.TextRange.Font.Color.RGB = RGB(248, 250, 252)
        .TextFrame.MarginLeft = 15
        .TextFrame.MarginTop = 15
    End With
    
    ' Team Analytics
    Set shp = slide.Shapes.AddRoundedRectangle(350, 120, 280, 350)
    With shp
        .Fill.ForeColor.RGB = RGB(30, 41, 59)
        .Line.ForeColor.RGB = RGB(59, 130, 246)
        .Line.Weight = 2
        .TextFrame.TextRange.Text = "✅ TEAM ANALYTICS" & vbCrLf & vbCrLf & _
            "• Team Heatmaps" & vbCrLf & _
            "• Possession % (Team)" & vbCrLf & _
            "• Pass Network Graph" & vbCrLf & _
            "• Successful Pass %" & vbCrLf & _
            "• Attacking Zone Entries" & vbCrLf & _
            "• Switches of Play" & vbCrLf & _
            "• Ball Recoveries" & vbCrLf & _
            "• Press Intensity (PPDA)" & vbCrLf & _
            "• Team Shape Analysis" & vbCrLf & _
            "• Tactical Formations" & vbCrLf & _
            "• Defensive Actions"
        .TextFrame.TextRange.Font.Name = "Segoe UI"
        .TextFrame.TextRange.Font.Size = 12
        .TextFrame.TextRange.Font.Color.RGB = RGB(248, 250, 252)
        .TextFrame.MarginLeft = 15
        .TextFrame.MarginTop